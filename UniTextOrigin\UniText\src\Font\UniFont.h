#ifndef __UniFont_h__
#define __UniFont_h__

#include "Common/UniObject.h"
#include "UniFontAtlasEntry.h"
#include "UniFontFallback.h"

#include <vector>
#include <string>
#include <string_view>
#include <map>
#include <unordered_map>
#include <memory>
#include <functional>

NAMESPACE_BEGIN

// Forward declarations
class IUniFontImpl;
struct TextureEntry;

/**
 * @brief Main font class for text rendering
 *
 * This class provides a unified interface for font operations with support
 * for different font backends through the IUniFontImpl interface. It handles
 * font loading, glyph caching, atlas management, and fallback font support
 * while allowing different backends (FreeType, custom, etc.) to be used interchangeably.
 *
 * Resources:
 * - Distance field fonts: https://github.com/libgdx/libgdx/wiki/Distance-field-fonts
 * - Multi channel sdf: https://github.com/Chlumsky/msdfgen
 * - Harfbuzz: https://harfbuzz.github.io/
 * - GPU Text: https://www.youtube.com/watch?v=6l_oSHWFbb0 | https://github.com/copperspice/copperspice
 * - Glyphy: https://github.com/behdad/glyphy
 */
class UniFont : public UniObject
{
public:
    /**
     * @brief Create a font from a file
     * @param fontPath Path to the font file
     * @return Unique pointer to the created font
     */
    static std::unique_ptr<UniFont> CreateFromFile(const char* fontPath);

    /**
     * @brief Create a font from memory
     * @param fontName Name to give the font (used for GUID generation)
     * @param fileBuffer Pointer to the font data in memory
     * @param bufferSize Size of the font data in bytes
     * @return Unique pointer to the created font
     */
    static std::unique_ptr<UniFont> CreateFromMemory(const char* fontName, const unsigned char* fileBuffer, unsigned long bufferSize);

    /**
     * @brief Initialize system fonts
     */
    static void InitializeSystemFonts();

    /**
     * @brief Destroy system fonts
     */
    static void DestroySystemFonts();

    /**
     * @brief Update all fonts if settings have changed
     *
     * This method checks if font settings have changed and updates all fonts accordingly.
     * It should be called periodically, especially after changing font settings.
     */
    static void UpdateAllFontsIfSettingsChanged();

public:
    /**
     * @brief Constructor
     * @param fontName Font name (used to generate GUID)
     * @param impl Implementation to use (defaults to FreeType)
     */
    UniFont(const char* fontName, std::unique_ptr<IUniFontImpl> impl = nullptr);

    /**
     * @brief Destructor
     */
    ~UniFont() noexcept;

    /**
     * @brief Set font properties
     * @param props Font properties
     */
    void Setup(const FontProperties& props);

    /**
     * @brief Get the font GUID
     * @return Font GUID
     */
    int GetFontGUID() const;

    /**
     * @brief Set the packing method
     * @param packingMethod Packing method
     */
    void SetPackingMethod(PackingMethod packingMethod) noexcept;

    /**
     * @brief Set the padding
     * @param padding Padding value
     */
    void SetPadding(uint8 padding) noexcept;

    /**
     * @brief Set the render mode
     * @param renderMode Render mode
     */
    void SetRenderMode(RenderMode renderMode) noexcept;

    /**
     * @brief Set the replacement character for missing glyphs
     * @param character Unicode codepoint for replacement character (default: U+FFFD)
     */
    void SetReplacementChar(uint32 character) noexcept;

    /**
     * @brief Get the replacement character
     * @return Unicode codepoint of the replacement character
     */
    uint32 GetReplacementChar() const noexcept;

    /**
     * @brief Add a fallback font
     * @param fontName Name of the fallback font
     */
    void AddFallbackFont(const char* fontName);

    /**
     * @brief Get the kerning between two characters
     * @param first First character
     * @param second Second character
     * @return Kerning value
     */
    float GetKerning(uint16 first, uint16 second) const noexcept;

    /**
     * @brief Get the ascender value for a given font size
     * @param fontSize Font size in pixels
     * @return Ascender value
     */
    float GetAscender(int fontSize) const noexcept;

    /**
     * @brief Get the descender value for a given font size
     * @param fontSize Font size in pixels
     * @return Descender value
     */
    float GetDescender(int fontSize) const noexcept;

    /**
     * @brief Get the line spacing for a given font size
     * @param fontSize Font size in pixels
     * @return Line spacing value
     */
    float GetLineSpacing(int fontSize) const noexcept;

    /**
     * @brief Get the font family name
     * @return Font family name extracted from the font file
     */
    const std::string& GetFamilyName() const;

#if USE_HARFBUZZ
    /**
     * @brief Get raw font data for HarfBuzz integration
     * @param outData Pointer to receive the font data pointer
     * @param outSize Pointer to receive the font data size
     * @return true if font data is available
     */
    bool GetFontData(const unsigned char** outData, unsigned long* outSize) const;

    /**
     * @brief Get FreeType face for HarfBuzz integration
     * @return FreeType face pointer (FT_Face) or nullptr if not available
     */
    void* GetFreeTypeFace() const;
#endif

    /**
     * @brief Get the font style
     * @return Font style extracted from the font file
     */
    FontStyle GetFontStyle() const;

    /**
     * @brief Get the padding
     * @return Padding value
     */
    uint8 GetPadding() const noexcept;

    /**
     * @brief Get the render mode
     * @return Render mode
     */
    RenderMode GetRenderMode() const noexcept;

    /**
     * @brief Get the minimum font size
     * @return Minimum font size
     */
    int GetMinFontSize() const noexcept;

    /**
     * @brief Get the maximum font size
     * @return Maximum font size
     */
    int GetMaxFontSize() const noexcept;

    /**
     * @brief Get the character index for a given Unicode character
     * @param c Unicode character
     * @param thread_id Thread ID (for multi-threaded implementations)
     * @return Character index, 0 if character is not supported
     */
    uint32 GetCharIndex(const uint16 c, uint32 thread_id = 0) const;

    void SetFontSizeRange(int minSize, int maxSize) noexcept;

    /**
     * @brief Get the glyph page for a given character and font size
     * @param unicode Unicode character
     * @param fontSize Font size in pixels
     * @return Pointer to the glyph page, or nullptr if not found
     */
    //const SizedPage* GetGlyphPage(uint16 unicode, int fontSize) const noexcept;

    /**
     * @brief Get the glyph page for a given font size and glyph info
     * @param fontSize Font size in pixels
     * @param glyph Glyph info
     * @return Pointer to the glyph page, or nullptr if not found
     */
    const SizedPage* GetGlyphPage(int fontSize, const GlyphInfo& glyph) const noexcept;

    /**
     * @brief Get the font atlas for a given font size and index
     * @param fontSize Font size in pixels
     * @param index Atlas index
     * @return Pointer to the texture, or nullptr if not found
     */
    const ITexture* GetFontAtlas(int fontSize, int index = 0) const;

    /**
     * @brief Get the glyph packer for a given font size and index
     * @param fontSize Font size in pixels
     * @param index Packer index
     * @return Pointer to the glyph packer, or nullptr if not found
     */
    const IUniGlyphPacker* GetAtlasPacker(int fontSize, int index = 0) const;

    /**
     * @brief Set the font size for rendering
     * @param fontSize Font size in pixels
     */
    void SetFontSizeForRendering(int fontSize);

    /**
     * @brief Load a glyph
     * @param inParam Glyph load parameters containing character, fontSize, allowSystemFallbacks, and renderMode
     * @param out Pointer to the glyph info
     * @return true if the glyph was loaded successfully, false otherwise
     */
    template<bool renderGlyph = true>
    bool LoadGlyph(const GlyphLoadParam& inParam, GlyphInfo** out);

#if USE_THREAD_POOL
    /**
     * @brief Load a glyph asynchronously
     * @param c Unicode character
     * @param fontSize Font size in pixels
     * @param out Pointer to the glyph info
     */
    void LoadGlyphAsync(const uint16 c, int fontSize, GlyphInfo** out);

    /**
     * @brief Finish all async jobs
     */
    void FinishAsyncJobs();
#endif

    /**
     * @brief Unload a glyph
     * @param c Unicode character
     * @param fontSize Font size in pixels
     */
    void UnloadGlyph(const uint16 c, int fontSize);

    /**
     * @brief Unload all unused glyphs
     * @return Number of glyphs unloaded
     */
    int UnloadUnusedGlyphs();

    /**
     * @brief Clear all glyphs
     */
    void ClearGlyphs();

private:
    // Private implementation methods
    bool InitLibrary();
    bool CreateFace(const char* filePath);
    bool CreateMemoryFace(const unsigned char* fileBytes, unsigned long fileSize);

    /// @brief Get a font that can render the glyph in fallbacks
    /// @param inParam 
    /// @param outFont 
    /// @return 
    bool GetFontForGlyphInFallbacks(const GlyphLoadParam& inParam, UniFont** outFont);
    IUniFontImpl* GetImpl() const noexcept { return m_pImpl.get(); }
    IUniFontImpl* GetImplForRendering(const GlyphLoadParam& inParam) noexcept;

#if USE_THREAD_POOL
    struct JobData
    {
        uint16 unicode;
        int fontSize;
        GlyphInfo* out;
    };
    std::vector<JobData> jobDataList;
    void LoadGlyphAsyncInternal(size_t jobDataIndex, unsigned int thread_id);
#endif

    bool TryPackGlyph(const uint16 unicode, const int desiredFontSize, const unsigned char* bitmap,
                      GlyphInfo& out, short& pageIndex);

    TextureEntry* TryPackGlyph(const uint16 unicode, const int roundedSize, GlyphInfo& out);
    void CacheGlyph(const uint16 unicode, const int desiredFontSize, GlyphInfo& out);

private:
    FontProperties m_FontProperties;
    std::unique_ptr<UniFontAtlasEntry> m_AtlasEntry;
    std::vector<UniFont*> m_FallbackFonts;

    // Implementation using the IUniFontImpl interface
    std::unique_ptr<IUniFontImpl> m_pImpl;
};

/**
 * @brief Cache for loading and managing fonts & font families
 */
class UniFontCache
{
public:
    /**
     * @brief Apply a function to each font in the cache
     * @param loopFunc Function to apply
     */
    inline void ForeachFont(std::function<void(UniFont*)> loopFunc)
    {
        auto it = m_FontCache.begin();
        while (it != m_FontCache.end())
        {
            loopFunc(it->second.get());
            it++;
        }
    }

    /**
     * @brief Get a font from the cache, loading it if necessary
     * @param fontPath Path to the font file
     * @return Pointer to the font
     */
    template<bool performLoad = true>
    UniFont* GetFont(const char* fontPath);

    /**
     * @brief Get a font from the cache, loading it if necessary
     * @param fontGuid Font GUID
     * @return Pointer to the font
     */
    UniFont* GetFont(int fontGuid);

    /**
     * @brief Load a font from memory
     * @param fontName Name to give the font (used for GUID generation)
     * @param fontBuffer Pointer to the font data in memory
     * @param bufferSize Size of the font data in bytes
     * @return Pointer to the loaded font
     */
    UniFont* LoadFontFromMemory(const char* fontName, const unsigned char* fontBuffer, unsigned long bufferSize);

    UniFontCache() = default;
    ~UniFontCache() = default;

private:
    /**
     * @brief Map of font GUIDs to font instances
     * key: font GUID.
     */
    std::unordered_map<int, std::unique_ptr<UniFont>> m_FontCache;

    /**
     * @brief Map of font paths to GUIDs for path-based access
     * key: font path, value: font GUID.
     */
    std::unordered_map<std::string, int> m_PathToGuidMap;
};

NAMESPACE_END

#endif // __UniFont_h__